# 编辑器优化设计文档

## 概述

本设计文档基于对 `frontend/src/editor` 目录的深入分析，提出了一套全面的优化方案。该方案旨在解决当前代码中存在的性能瓶颈、架构不一致、类型安全问题和内存管理问题。

## 架构

### 当前架构分析

当前编辑器采用以下架构模式：

- **MobX 状态管理**：使用 Store 类和多个 Manager 类管理状态
- **Fabric.js 画布渲染**：处理 2D 图形和交互
- **React 组件层**：UI 渲染和用户交互
- **时间线系统**：基于虚拟化列表的时间线管理

### 优化后的架构设计

#### 1. 分层架构模式

```
┌─────────────────────────────────────────┐
│              UI Layer (React)           │
├─────────────────────────────────────────┤
│           Service Layer                 │
│  ┌─────────────┬─────────────────────┐  │
│  │ Performance │   Memory Manager    │  │
│  │   Manager   │                     │  │
│  └─────────────┴─────────────────────┘  │
├─────────────────────────────────────────┤
│          Business Logic Layer           │
│  ┌─────────────┬─────────────────────┐  │
│  │   Store     │    Managers         │  │
│  │  (MobX)     │  (Element, Canvas)  │  │
│  └─────────────┴─────────────────────┘  │
├─────────────────────────────────────────┤
│           Data Layer                    │
│  ┌─────────────┬─────────────────────┐  │
│  │  Fabric.js  │   DOM Elements      │  │
│  │   Canvas    │                     │  │
│  └─────────────┴─────────────────────┘  │
└─────────────────────────────────────────┘
```

#### 2. 性能优化架构

**虚拟化渲染系统**

- 时间线元素虚拟化
- 可视区域计算优化
- 智能缓存机制

**内存管理系统**

- 全局 ResizeObserver 管理
- 事件监听器生命周期管理
- 缓存大小限制和清理

## 组件和接口

### 1. 核心接口重构

#### 统一的类型定义系统

```typescript
// 核心类型定义
export interface TimelineElement {
  id: string;
  type: ElementType;
  name: string;
  timeFrame: TimeFrame;
  placement: Placement;
  properties: ElementProperties;
  fabricObject?: fabric.Object;
  trackId?: string;
  locked?: boolean;
  opacity?: number;
}

// 性能优化相关接口
export interface PerformanceConfig {
  virtualScrolling: {
    enabled: boolean;
    itemHeight: number;
    overscanCount: number;
    bufferSize: number;
  };
  rendering: {
    debounceDelay: number;
    throttleDelay: number;
    maxCacheSize: number;
  };
  memory: {
    maxObservers: number;
    cleanupInterval: number;
    gcThreshold: number;
  };
}

// 事件处理接口
export interface EventHandlerConfig {
  passive: boolean;
  capture: boolean;
  once?: boolean;
}

export interface ManagedEventListener {
  element: Element | Window | Document;
  event: string;
  handler: EventListener;
  config: EventHandlerConfig;
  cleanup: () => void;
}
```

#### 组件 Props 标准化

```typescript
// 标准化的组件Props
export interface BaseComponentProps {
  className?: string;
  style?: React.CSSProperties;
  "data-testid"?: string;
}

export interface TimelineComponentProps extends BaseComponentProps {
  element: TimelineElement;
  containerWidth: number;
  onTimeFrameChange: (element: TimelineElement, timeFrame: TimeFrame) => void;
  isSelected?: boolean;
  isDragging?: boolean;
}

export interface PerformantComponentProps extends BaseComponentProps {
  shouldUpdate?: (prevProps: any, nextProps: any) => boolean;
  memoKey?: string;
  debugName?: string;
}
```

### 2. 性能管理器

#### PerformanceManager 类设计

```typescript
export class PerformanceManager {
  private config: PerformanceConfig;
  private metrics: PerformanceMetrics;
  private observers: Map<string, Observer>;

  constructor(config: PerformanceConfig) {
    this.config = config;
    this.metrics = new PerformanceMetrics();
    this.observers = new Map();
  }

  // 虚拟化管理
  createVirtualizedList(config: VirtualListConfig): VirtualList;

  // 渲染优化
  createOptimizedRenderer(): Renderer;

  // 内存监控
  startMemoryMonitoring(): void;
  stopMemoryMonitoring(): void;

  // 性能指标收集
  collectMetrics(): PerformanceReport;
}
```

#### MemoryManager 类设计

```typescript
export class MemoryManager {
  private eventListeners: Set<ManagedEventListener>;
  private observers: Map<Element, ResizeObserver>;
  private caches: Map<string, Cache>;
  private cleanupTasks: Set<CleanupTask>;

  constructor() {
    this.eventListeners = new Set();
    this.observers = new Map();
    this.caches = new Map();
    this.cleanupTasks = new Set();
  }

  // 事件监听器管理
  addEventListener(config: EventListenerConfig): ManagedEventListener;
  removeEventListener(listener: ManagedEventListener): void;

  // Observer管理
  createResizeObserver(callback: ResizeObserverCallback): ResizeObserver;
  destroyResizeObserver(observer: ResizeObserver): void;

  // 缓存管理
  createCache<T>(name: string, maxSize: number): Cache<T>;
  clearCache(name: string): void;

  // 清理管理
  registerCleanupTask(task: CleanupTask): void;
  executeCleanup(): void;
}
```

### 3. 优化后的组件架构

#### 高性能时间线组件

```typescript
// 优化后的TimeFrameView组件
export const TimeFrameView = React.memo(
  forwardRef<HTMLDivElement, TimeFrameViewProps>((props, ref) => {
    const {
      element,
      containerWidth,
      onTimeFrameChange,
      isSelected,
      isDragging,
      ...restProps
    } = props;

    // 使用性能优化的hooks
    const { elementStyles, handleStyles, backgroundStyles } =
      useOptimizedStyles(element, isSelected, isDragging);

    const { handleMouseDown, handleLeftHandleDrag, handleRightHandleDrag } =
      useOptimizedEventHandlers(element, onTimeFrameChange);

    // 使用内存管理的ResizeObserver
    const { elementWidth } = useOptimizedResize(ref);

    return (
      <div
        ref={ref}
        className="timeline-element"
        style={elementStyles}
        onMouseDown={handleMouseDown}
        {...restProps}
      >
        {/* 优化后的子组件 */}
      </div>
    );
  }),
  // 精确的比较函数
  (prevProps, nextProps) => {
    return (
      prevProps.element.id === nextProps.element.id &&
      prevProps.element.timeFrame.start === nextProps.element.timeFrame.start &&
      prevProps.element.timeFrame.end === nextProps.element.timeFrame.end &&
      prevProps.containerWidth === nextProps.containerWidth &&
      prevProps.isSelected === nextProps.isSelected &&
      prevProps.isDragging === nextProps.isDragging
    );
  }
);
```

#### 优化的 Hook 系统

```typescript
// 性能优化的样式Hook
export function useOptimizedStyles(
  element: TimelineElement,
  isSelected: boolean,
  isDragging: boolean
) {
  return useMemo(() => {
    const elementColor = ELEMENT_COLORS[element.type] || ELEMENT_COLORS.default;

    return {
      elementStyles: {
        backgroundColor: isSelected ? elementColor : alpha(elementColor, 0.8),
        border: isDragging ? "2px solid #2196f3" : "none",
        // ... 其他样式
      },
      handleStyles: {
        backgroundColor: "white",
        borderColor: elementColor,
        // ... 其他样式
      },
      backgroundStyles: createBackgroundStyles(element),
    };
  }, [element.type, element.properties?.src, isSelected, isDragging]);
}

// 优化的事件处理Hook
export function useOptimizedEventHandlers(
  element: TimelineElement,
  onTimeFrameChange: TimeFrameChangeHandler
) {
  const memoryManager = useMemoryManager();

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      // 优化的事件处理逻辑
    },
    [element.id]
  );

  const handleLeftHandleDrag = useCallback(
    (e: React.MouseEvent) => {
      // 优化的拖拽处理逻辑
    },
    [element.id, onTimeFrameChange]
  );

  const handleRightHandleDrag = useCallback(
    (e: React.MouseEvent) => {
      // 优化的拖拽处理逻辑
    },
    [element.id, onTimeFrameChange]
  );

  // 注册清理任务
  useEffect(() => {
    const cleanupTask = () => {
      // 清理事件监听器
    };
    memoryManager.registerCleanupTask(cleanupTask);
    return cleanupTask;
  }, []);

  return {
    handleMouseDown,
    handleLeftHandleDrag,
    handleRightHandleDrag,
  };
}
```

## 数据模型

### 1. 优化的状态管理模型

#### 分层状态结构

```typescript
// 应用状态分层
interface AppState {
  // UI状态层
  ui: {
    selectedElements: string[];
    dragState: DragState;
    viewportState: ViewportState;
    loadingStates: Map<string, LoadingState>;
  };

  // 业务逻辑层
  editor: {
    elements: Map<string, TimelineElement>;
    tracks: Map<string, Track>;
    animations: Map<string, Animation>;
    canvas: CanvasState;
  };

  // 性能状态层
  performance: {
    metrics: PerformanceMetrics;
    cache: CacheState;
    memory: MemoryState;
  };
}
```

#### 优化的元素数据模型

```typescript
// 优化后的元素模型
export class OptimizedElement {
  readonly id: string;
  readonly type: ElementType;
  private _timeFrame: TimeFrame;
  private _placement: Placement;
  private _properties: ElementProperties;
  private _fabricObject?: fabric.Object;
  private _isDirty: boolean = false;
  private _lastUpdate: number = 0;

  constructor(data: ElementData) {
    this.id = data.id;
    this.type = data.type;
    this._timeFrame = data.timeFrame;
    this._placement = data.placement;
    this._properties = data.properties;
  }

  // 优化的getter/setter
  get timeFrame(): TimeFrame {
    return this._timeFrame;
  }

  set timeFrame(value: TimeFrame) {
    if (this.isTimeFrameChanged(value)) {
      this._timeFrame = value;
      this.markDirty();
    }
  }

  // 性能优化方法
  private isTimeFrameChanged(newTimeFrame: TimeFrame): boolean {
    return (
      this._timeFrame.start !== newTimeFrame.start ||
      this._timeFrame.end !== newTimeFrame.end
    );
  }

  private markDirty(): void {
    this._isDirty = true;
    this._lastUpdate = performance.now();
  }

  // 批量更新方法
  batchUpdate(updates: Partial<ElementData>): void {
    let hasChanges = false;

    if (updates.timeFrame && this.isTimeFrameChanged(updates.timeFrame)) {
      this._timeFrame = updates.timeFrame;
      hasChanges = true;
    }

    if (updates.placement && this.isPlacementChanged(updates.placement)) {
      this._placement = updates.placement;
      hasChanges = true;
    }

    if (hasChanges) {
      this.markDirty();
    }
  }
}
```

### 2. 缓存和索引系统

#### 智能缓存模型

```typescript
export class SmartCache<T> {
  private cache: Map<string, CacheEntry<T>>;
  private maxSize: number;
  private ttl: number;
  private accessOrder: string[];

  constructor(maxSize: number = 100, ttl: number = 300000) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
    this.accessOrder = [];
  }

  get(key: string): T | undefined {
    const entry = this.cache.get(key);
    if (!entry) return undefined;

    // 检查TTL
    if (Date.now() - entry.timestamp > this.ttl) {
      this.delete(key);
      return undefined;
    }

    // 更新访问顺序
    this.updateAccessOrder(key);
    return entry.value;
  }

  set(key: string, value: T): void {
    // 检查缓存大小限制
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      accessCount: 1,
    });

    this.updateAccessOrder(key);
  }

  private evictLRU(): void {
    const lruKey = this.accessOrder[0];
    if (lruKey) {
      this.delete(lruKey);
    }
  }
}
```

#### 索引系统

```typescript
export class ElementIndexManager {
  private timeIndex: Map<number, Set<string>>;
  private typeIndex: Map<ElementType, Set<string>>;
  private trackIndex: Map<string, Set<string>>;
  private spatialIndex: QuadTree<string>;

  constructor() {
    this.timeIndex = new Map();
    this.typeIndex = new Map();
    this.trackIndex = new Map();
    this.spatialIndex = new QuadTree();
  }

  // 添加元素到索引
  addElement(element: TimelineElement): void {
    this.addToTimeIndex(element);
    this.addToTypeIndex(element);
    this.addToTrackIndex(element);
    this.addToSpatialIndex(element);
  }

  // 查询指定时间范围内的元素
  getElementsInTimeRange(start: number, end: number): string[] {
    const result = new Set<string>();

    for (let time = start; time <= end; time += 1000) {
      const elements = this.timeIndex.get(time);
      if (elements) {
        elements.forEach((id) => result.add(id));
      }
    }

    return Array.from(result);
  }

  // 查询指定区域内的元素
  getElementsInRegion(bounds: Rectangle): string[] {
    return this.spatialIndex.query(bounds);
  }
}
```

## 错误处理

### 1. 错误边界系统

#### React 错误边界

```typescript
export class EditorErrorBoundary extends React.Component<
  ErrorBoundaryProps,
  ErrorBoundaryState
> {
  private errorReportingService: ErrorReportingService;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
    this.errorReportingService = new ErrorReportingService();
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // 报告错误
    this.errorReportingService.reportError(error, {
      component: this.props.componentName,
      errorInfo,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorFallback
          error={this.state.error}
          onRetry={() => this.setState({ hasError: false })}
          onReport={() => this.reportError()}
        />
      );
    }

    return this.props.children;
  }
}
```

#### 异步错误处理

```typescript
export class AsyncErrorHandler {
  private errorQueue: AsyncError[];
  private isProcessing: boolean = false;
  private retryConfig: RetryConfig;

  constructor(retryConfig: RetryConfig) {
    this.errorQueue = [];
    this.retryConfig = retryConfig;
  }

  async handleAsyncOperation<T>(
    operation: () => Promise<T>,
    context: OperationContext
  ): Promise<Result<T>> {
    try {
      const result = await this.executeWithRetry(operation, context);
      return { success: true, data: result };
    } catch (error) {
      return this.handleError(error, context);
    }
  }

  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    context: OperationContext
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= this.retryConfig.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        if (attempt < this.retryConfig.maxAttempts) {
          await this.delay(this.retryConfig.delayMs * attempt);
        }
      }
    }

    throw lastError!;
  }

  private handleError<T>(error: Error, context: OperationContext): Result<T> {
    const errorInfo: AsyncError = {
      error,
      context,
      timestamp: Date.now(),
      retryCount: 0,
    };

    this.errorQueue.push(errorInfo);
    this.processErrorQueue();

    return {
      success: false,
      error: error.message,
      canRetry: this.canRetry(error),
    };
  }
}
```

### 2. 输入验证和边界检查

#### 输入验证系统

```typescript
export class InputValidator {
  private validationRules: Map<string, ValidationRule[]>;

  constructor() {
    this.validationRules = new Map();
    this.setupDefaultRules();
  }

  private setupDefaultRules(): void {
    // 时间帧验证规则
    this.addRule("timeFrame", {
      name: "validTimeRange",
      validate: (timeFrame: TimeFrame) => {
        return (
          timeFrame.start >= 0 &&
          timeFrame.end > timeFrame.start &&
          timeFrame.end <= MAX_TIMELINE_DURATION
        );
      },
      message: "Invalid time range",
    });

    // 位置验证规则
    this.addRule("placement", {
      name: "validPlacement",
      validate: (placement: Placement) => {
        return (
          placement.x >= 0 &&
          placement.y >= 0 &&
          placement.width > 0 &&
          placement.height > 0
        );
      },
      message: "Invalid placement values",
    });
  }

  validate<T>(type: string, value: T): ValidationResult {
    const rules = this.validationRules.get(type);
    if (!rules) {
      return { isValid: true };
    }

    for (const rule of rules) {
      if (!rule.validate(value)) {
        return {
          isValid: false,
          error: rule.message,
          rule: rule.name,
        };
      }
    }

    return { isValid: true };
  }
}
```

#### 边界检查系统

```typescript
export class BoundaryChecker {
  private canvasBounds: Rectangle;
  private timelineBounds: TimeRange;

  constructor(canvasBounds: Rectangle, timelineBounds: TimeRange) {
    this.canvasBounds = canvasBounds;
    this.timelineBounds = timelineBounds;
  }

  checkElementBounds(element: TimelineElement): BoundaryCheckResult {
    const issues: BoundaryIssue[] = [];

    // 检查时间边界
    if (element.timeFrame.start < this.timelineBounds.start) {
      issues.push({
        type: "time",
        severity: "error",
        message: "Element starts before timeline beginning",
      });
    }

    if (element.timeFrame.end > this.timelineBounds.end) {
      issues.push({
        type: "time",
        severity: "warning",
        message: "Element extends beyond timeline end",
      });
    }

    // 检查空间边界
    const elementBounds = this.getElementBounds(element);
    if (!this.isWithinCanvas(elementBounds)) {
      issues.push({
        type: "spatial",
        severity: "warning",
        message: "Element extends beyond canvas bounds",
      });
    }

    return {
      isValid: issues.length === 0,
      issues,
      correctedElement: this.correctBoundaries(element, issues),
    };
  }

  private correctBoundaries(
    element: TimelineElement,
    issues: BoundaryIssue[]
  ): TimelineElement {
    const corrected = { ...element };

    for (const issue of issues) {
      if (issue.type === "time" && issue.severity === "error") {
        // 自动修正时间边界
        corrected.timeFrame = this.clampTimeFrame(corrected.timeFrame);
      }

      if (issue.type === "spatial" && issue.severity === "error") {
        // 自动修正空间边界
        corrected.placement = this.clampPlacement(corrected.placement);
      }
    }

    return corrected;
  }
}
```

## 测试策略

### 1. 性能测试

#### 性能基准测试

```typescript
export class PerformanceBenchmark {
  private metrics: PerformanceMetrics;
  private testSuites: Map<string, TestSuite>;

  constructor() {
    this.metrics = new PerformanceMetrics();
    this.testSuites = new Map();
  }

  // 时间线性能测试
  async benchmarkTimeline(elementCount: number): Promise<BenchmarkResult> {
    const testData = this.generateTestElements(elementCount);

    const startTime = performance.now();

    // 测试渲染性能
    const renderTime = await this.measureRenderTime(testData);

    // 测试交互性能
    const interactionTime = await this.measureInteractionTime(testData);

    // 测试内存使用
    const memoryUsage = await this.measureMemoryUsage(testData);

    const totalTime = performance.now() - startTime;

    return {
      elementCount,
      renderTime,
      interactionTime,
      memoryUsage,
      totalTime,
      fps: this.calculateFPS(renderTime),
      passed: this.evaluatePerformance(
        renderTime,
        interactionTime,
        memoryUsage
      ),
    };
  }

  // 内存泄漏测试
  async testMemoryLeaks(): Promise<MemoryLeakResult> {
    const initialMemory = this.getMemoryUsage();

    // 执行多次创建和销毁操作
    for (let i = 0; i < 100; i++) {
      await this.createAndDestroyElements();

      // 强制垃圾回收（如果可用）
      if (window.gc) {
        window.gc();
      }
    }

    const finalMemory = this.getMemoryUsage();
    const memoryDiff = finalMemory - initialMemory;

    return {
      initialMemory,
      finalMemory,
      memoryDiff,
      hasLeak: memoryDiff > MEMORY_LEAK_THRESHOLD,
      leakRate: memoryDiff / 100, // per operation
    };
  }
}
```

### 2. 集成测试

#### 组件集成测试

```typescript
describe("Timeline Integration Tests", () => {
  let store: Store;
  let performanceManager: PerformanceManager;
  let memoryManager: MemoryManager;

  beforeEach(() => {
    store = new Store();
    performanceManager = new PerformanceManager(defaultConfig);
    memoryManager = new MemoryManager();
  });

  afterEach(() => {
    memoryManager.executeCleanup();
  });

  test("should handle large number of elements without performance degradation", async () => {
    const elements = generateTestElements(1000);

    const startTime = performance.now();

    // 添加元素到时间线
    elements.forEach((element) => store.addEditorElement(element));

    // 渲染时间线
    const { container } = render(
      <TimelineProvider>
        <Timeline elements={elements} />
      </TimelineProvider>
    );

    const renderTime = performance.now() - startTime;

    // 验证性能要求
    expect(renderTime).toBeLessThan(1000); // 1秒内完成渲染

    // 验证内存使用
    const memoryUsage = memoryManager.getCurrentUsage();
    expect(memoryUsage.heapUsed).toBeLessThan(100 * 1024 * 1024); // 100MB限制
  });

  test("should properly cleanup resources on unmount", async () => {
    const { unmount } = render(<Timeline elements={testElements} />);

    const initialListeners = memoryManager.getActiveListeners().size;
    const initialObservers = memoryManager.getActiveObservers().size;

    unmount();

    // 等待清理完成
    await waitFor(() => {
      expect(memoryManager.getActiveListeners().size).toBe(0);
      expect(memoryManager.getActiveObservers().size).toBe(0);
    });
  });
});
```

### 3. 单元测试

#### Hook 测试

```typescript
describe("useOptimizedStyles", () => {
  test("should memoize styles correctly", () => {
    const element = createTestElement();

    const { result, rerender } = renderHook(
      ({ element, isSelected }) =>
        useOptimizedStyles(element, isSelected, false),
      { initialProps: { element, isSelected: false } }
    );

    const firstResult = result.current;

    // 重新渲染但props没变化
    rerender({ element, isSelected: false });

    // 应该返回相同的对象引用
    expect(result.current).toBe(firstResult);

    // 改变props
    rerender({ element, isSelected: true });

    // 应该返回新的对象引用
    expect(result.current).not.toBe(firstResult);
  });
});
```

这个设计文档提供了一个全面的优化方案，涵盖了架构重构、性能优化、内存管理、错误处理和测试策略等各个方面。通过这些优化，编辑器将具有更好的性能、更高的可维护性和更强的稳定性。
