import { useHotkeys } from "react-hotkeys-hook";
import { Store } from "../store/Store";
import { useLanguage } from "../i18n/LanguageContext";

// 快捷键类型定义
export interface ShortcutKey {
  key: string;
  description: string;
  category: string;
}

// 快捷键配置接口
export interface ShortcutConfig {
  keys: string[];
  handler: (e: KeyboardEvent) => void;
  options?: {
    enableOnFormTags?: boolean;
    preventDefault?: boolean;
    enabled?: boolean;
  };
}

// 快捷键管理器
export class ShortcutManager {
  private store: Store;
  private t: (key: string) => string;
  private lastPlayPauseTime: number = 0;
  private readonly PLAY_PAUSE_DEBOUNCE = 200; // 200ms 防抖

  constructor(store: Store, t: (key: string) => string) {
    this.store = store;
    this.t = t;
  }

  // 获取所有快捷键配置
  getShortcutConfigs(): Record<string, ShortcutConfig> {
    return {
      // 编辑操作
      undo: {
        keys: ["ctrl+z", "meta+z"],
        handler: () => {
          const actionType = this.store.getUndoActionType();
          if (actionType) {
            this.store.undo();
          }
        },
      },
      redo: {
        keys: ["ctrl+y", "meta+shift+z"],
        handler: () => {
          const actionType = this.store.getRedoActionType();
          if (actionType) {
            this.store.redo();
          }
        },
      },
      delete: {
        keys: ["del", "delete", "backspace"],
        handler: (e) => {
          // 防止在输入框中触发删除
          if (this.isInputElement(e.target as HTMLElement)) {
            return;
          }

          e.preventDefault();

          // 首先检查是否有选中的字幕
          const selectedCaption = this.store.getSelectedCaption();
          if (selectedCaption) {
            console.log(
              `删除字幕: ${selectedCaption.text} (ID: ${selectedCaption.id})`
            );
            this.store.deleteCaption(selectedCaption.id);
            return;
          }

          // 检查是否有多选元素
          const selectedElements = this.store.selectedElements;
          if (selectedElements.length > 0) {
            console.log(`批量删除 ${selectedElements.length} 个元素`);
            // 批量删除多选元素
            selectedElements.forEach((element) => {
              console.log(`删除元素: ${element.name} (ID: ${element.id})`);
              this.store.deleteElement(element.id);
            });
            // 清除多选状态
            this.store.setSelectedElements([]);
            return;
          }

          // 如果没有选中的字幕和多选元素，检查是否有选中的时间线元素
          // 优先检查 selectedElement（包括audio等没有fabricObject的元素）
          const selectedElement = this.store.selectedElement;
          if (selectedElement) {
            this.store.deleteElement(selectedElement.id);
            return;
          }

          // 最后检查canvas上的活动元素（有fabricObject的元素）
          const activeElement = this.store.elementManager.getActiveElement();
          if (activeElement) {
            console.log(
              `删除活动元素: ${activeElement.name} (ID: ${activeElement.id})`
            );
            this.store.deleteElement(activeElement.id);
          }
        },
        options: {
          enableOnFormTags: false,
          preventDefault: true,
        },
      },

      // 全选功能
      selectAll: {
        keys: ["ctrl+a", "meta+a"],
        handler: (e) => {
          // 防止在输入框中触发全选
          if (this.isInputElement(e.target as HTMLElement)) {
            return;
          }

          e.preventDefault();
          e.stopPropagation();

          // 在播放状态下不允许全选
          if (this.store.playing) {
            console.log("播放状态下不允许全选");
            return;
          }

          // 执行全选操作
          this.store.selectAllTimelineElements();
        },
        options: {
          enableOnFormTags: false,
          preventDefault: true,
        },
      },

      // 取消选择/框选
      escape: {
        keys: ["escape"],
        handler: (e) => {
          // 防止在输入框中触发
          if (this.isInputElement(e.target as HTMLElement)) {
            return;
          }

          e.preventDefault();
          e.stopPropagation();

          // 清除所有选择状态
          this.store.clearAllSelections();

          // 触发自定义事件，通知框选组件取消框选
          window.dispatchEvent(new CustomEvent("timeline-cancel-selection"));
        },
        options: {
          enableOnFormTags: false,
          preventDefault: true,
        },
      },

      // 工具切换
      moveMode: {
        keys: ["v"],
        handler: () => {
          if (this.store.editMode !== "move") {
            this.store.setEditMode("move");
          }
        },
      },
      handTool: {
        keys: ["h"],
        handler: () => {
          if (this.store.editMode !== "hand") {
            this.store.setEditMode("hand");
          }
        },
      },

      // 视图操作
      zoomIn: {
        keys: ["ctrl+=", "ctrl+plus", "meta+=", "meta+plus"],
        handler: (e) => {
          e.preventDefault();
          const newValue = this.store.zoomIn();
          // 触发CanvasContainer中的MapInteractionCSS组件更新
          window.dispatchEvent(
            new CustomEvent("canvas-zoom-change", { detail: newValue })
          );
        },
        options: {
          enableOnFormTags: false,
          preventDefault: true,
        },
      },
      zoomOut: {
        keys: ["ctrl+-", "ctrl+minus", "meta+-", "meta+minus"],
        handler: (e) => {
          e.preventDefault();
          const newValue = this.store.zoomOut();
          window.dispatchEvent(
            new CustomEvent("canvas-zoom-change", { detail: newValue })
          );
        },
        options: {
          enableOnFormTags: false,
          preventDefault: true,
        },
      },
      fitToScreen: {
        keys: ["ctrl+0", "meta+0"],
        handler: (e) => {
          e.preventDefault();
          const newValue = this.store.resetZoom();
          window.dispatchEvent(
            new CustomEvent("canvas-zoom-change", { detail: newValue })
          );
        },
        options: {
          enableOnFormTags: false,
          preventDefault: true,
        },
      },

      // 播放控制
      playPause: {
        keys: ["space"],
        handler: (e) => {
          if (this.isInputElement(e.target as HTMLElement)) {
            return;
          }

          // 防抖处理，避免快速连续按键
          const now = Date.now();
          if (now - this.lastPlayPauseTime < this.PLAY_PAUSE_DEBOUNCE) {
            console.log("播放/暂停操作被防抖拦截");
            return;
          }
          this.lastPlayPauseTime = now;

          e.preventDefault();
          e.stopPropagation();

          if (!this.store.playing) {
            // 如果当前不在播放状态，检查是否已经播放结束
            const currentTime = this.store.currentTimeInMs;
            const maxDuration = this.store.maxDuration;

            // 如果当前时间已经到达或非常接近最大持续时间（考虑到帧精度），则从头开始播放
            if (maxDuration > 0 && currentTime >= maxDuration - 100) {
              this.store.handleSeek(0); // 跳转到开头
            }
          }

          this.store.setPlaying(!this.store.playing);
        },
        options: {
          enableOnFormTags: false,
          preventDefault: true,
        },
      },
      seekForward: {
        keys: ["arrowright"],
        handler: (e) => {
          if (this.isInputElement(e.target as HTMLElement)) {
            return;
          }
          e.preventDefault();
          this.store.handleSeek(this.store.currentTimeInMs + 5000);
        },
        options: {
          enableOnFormTags: false,
          preventDefault: true,
        },
      },
      seekBackward: {
        keys: ["arrowleft"],
        handler: (e) => {
          if (this.isInputElement(e.target as HTMLElement)) {
            return;
          }
          e.preventDefault();
          this.store.handleSeek(Math.max(0, this.store.currentTimeInMs - 5000));
        },
        options: {
          enableOnFormTags: false,
          preventDefault: true,
        },
      },
      fitTimeline: {
        keys: ["shift+f"],
        handler: (e) => {
          if (this.isInputElement(e.target as HTMLElement)) {
            return;
          }
          e.preventDefault();
          this.store.fitTimelineToContent();
        },
        options: {
          enableOnFormTags: false,
          preventDefault: true,
        },
      },

      // 其他操作
      showShortcuts: {
        keys: ["ctrl+k", "meta+k"],
        handler: (e) => {
          e.preventDefault();
          // 这个需要在使用的组件中处理
          window.dispatchEvent(new CustomEvent("show-shortcuts"));
        },
        options: {
          enableOnFormTags: false,
          preventDefault: true,
        },
      },
    };
  }

  // 获取快捷键显示列表
  getShortcutKeys(): ShortcutKey[] {
    return [
      // 编辑类别
      {
        key: "Ctrl+Z",
        description: this.t("shortcut_undo"),
        category: this.t("category_edit"),
      },
      {
        key: "Ctrl+Y",
        description: this.t("shortcut_redo"),
        category: this.t("category_edit"),
      },
      {
        key: "Delete",
        description: this.t("shortcut_delete_element"),
        category: this.t("category_edit"),
      },

      // 工具类别
      {
        key: "V",
        description: this.t("shortcut_move_mode"),
        category: this.t("category_tools"),
      },
      {
        key: "H",
        description: this.t("shortcut_hand_tool"),
        category: this.t("category_tools"),
      },

      // 视图类别
      {
        key: "Ctrl++",
        description: this.t("shortcut_zoom_in"),
        category: this.t("category_view"),
      },
      {
        key: "Ctrl+-",
        description: this.t("shortcut_zoom_out"),
        category: this.t("category_view"),
      },
      {
        key: "Ctrl+0",
        description: this.t("shortcut_fit_to_screen"),
        category: this.t("category_view"),
      },
      {
        key: "Shift+F",
        description: this.t("shortcut_fit_timeline"),
        category: this.t("category_view"),
      },

      // 播放类别
      {
        key: "Space",
        description: this.t("shortcut_play_pause"),
        category: this.t("category_playback"),
      },
      {
        key: "→",
        description: this.t("shortcut_seek_forward"),
        category: this.t("category_playback"),
      },
      {
        key: "←",
        description: this.t("shortcut_seek_backward"),
        category: this.t("category_playback"),
      },

      // 帮助类别
      {
        key: "Ctrl+K",
        description: this.t("shortcut_show_shortcuts"),
        category: this.t("category_help"),
      },
    ];
  }

  // 检查是否为输入元素
  private isInputElement(element: HTMLElement): boolean {
    return (
      element.tagName === "INPUT" ||
      element.tagName === "TEXTAREA" ||
      element.contentEditable === "true"
    );
  }
}

// 自定义Hook：使用快捷键管理器
export function useShortcuts(store: Store) {
  const { t } = useLanguage();
  const shortcutManager = new ShortcutManager(store, t);
  const configs = shortcutManager.getShortcutConfigs();

  // 注册所有快捷键 - 每个快捷键单独注册
  useHotkeys(
    configs.undo.keys,
    configs.undo.handler,
    { ...configs.undo.options, keydown: true, keyup: false },
    [store]
  );
  useHotkeys(
    configs.redo.keys,
    configs.redo.handler,
    { ...configs.redo.options, keydown: true, keyup: false },
    [store]
  );
  useHotkeys(
    configs.delete.keys,
    configs.delete.handler,
    { ...configs.delete.options, keydown: true, keyup: false },
    [store]
  );
  useHotkeys(
    configs.selectAll.keys,
    configs.selectAll.handler,
    { ...configs.selectAll.options, keydown: true, keyup: false },
    [store]
  );
  useHotkeys(
    configs.escape.keys,
    configs.escape.handler,
    { ...configs.escape.options, keydown: true, keyup: false },
    [store]
  );
  useHotkeys(
    configs.moveMode.keys,
    configs.moveMode.handler,
    { ...configs.moveMode.options, keydown: true, keyup: false },
    [store]
  );
  useHotkeys(
    configs.handTool.keys,
    configs.handTool.handler,
    { ...configs.handTool.options, keydown: true, keyup: false },
    [store]
  );
  useHotkeys(
    configs.zoomIn.keys,
    configs.zoomIn.handler,
    { ...configs.zoomIn.options, keydown: true, keyup: false },
    [store]
  );
  useHotkeys(
    configs.zoomOut.keys,
    configs.zoomOut.handler,
    { ...configs.zoomOut.options, keydown: true, keyup: false },
    [store]
  );
  useHotkeys(
    configs.fitToScreen.keys,
    configs.fitToScreen.handler,
    { ...configs.fitToScreen.options, keydown: true, keyup: false },
    [store]
  );
  useHotkeys(
    configs.playPause.keys,
    configs.playPause.handler,
    {
      ...configs.playPause.options,
      keydown: true,
      keyup: false,
      preventDefault: true,
    },
    [store]
  );
  useHotkeys(
    configs.seekForward.keys,
    configs.seekForward.handler,
    { ...configs.seekForward.options, keydown: true, keyup: false },
    [store]
  );
  useHotkeys(
    configs.seekBackward.keys,
    configs.seekBackward.handler,
    { ...configs.seekBackward.options, keydown: true, keyup: false },
    [store]
  );
  useHotkeys(
    configs.fitTimeline.keys,
    configs.fitTimeline.handler,
    { ...configs.fitTimeline.options, keydown: true, keyup: false },
    [store]
  );
  useHotkeys(
    configs.showShortcuts.keys,
    configs.showShortcuts.handler,
    { ...configs.showShortcuts.options, keydown: true, keyup: false },
    [store]
  );

  return {
    shortcutManager,
    shortcutKeys: shortcutManager.getShortcutKeys(),
  };
}
